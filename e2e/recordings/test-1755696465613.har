{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.54.2"}, "browser": {"name": "electron", "version": "138.0.7204.235"}, "entries": [{"startedDateTime": "2025-08-20T13:27:45.932Z", "time": 3.035, "request": {"method": "GET", "url": "app://obsidian.md/index.html", "httpVersion": "app", "cookies": [], "headers": [], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Content-Type", "value": "text/html"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/html"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 3.035}}, {"startedDateTime": "2025-08-20T13:27:46.017Z", "time": 5.764, "request": {"method": "GET", "url": "app://obsidian.md/app.css", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/css"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/css"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.764}}, {"startedDateTime": "2025-08-20T13:27:46.017Z", "time": 13.033, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/codemirror.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 13.033}}, {"startedDateTime": "2025-08-20T13:27:46.017Z", "time": 5.05, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/overlay.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.05}}, {"startedDateTime": "2025-08-20T13:27:46.017Z", "time": 4.773, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/markdown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.773}}, {"startedDateTime": "2025-08-20T13:27:46.017Z", "time": 4.885, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/cm-addons.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.885}}, {"startedDateTime": "2025-08-20T13:27:46.017Z", "time": 11.565, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/vim.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 11.565}}, {"startedDateTime": "2025-08-20T13:27:46.017Z", "time": 4.801, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/meta.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.801}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 15.619, "request": {"method": "GET", "url": "app://obsidian.md/lib/moment.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 15.619}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 36.556, "request": {"method": "GET", "url": "app://obsidian.md/lib/pixi.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 36.556}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 4.807, "request": {"method": "GET", "url": "app://obsidian.md/lib/i18next.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.807}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 4.897, "request": {"method": "GET", "url": "app://obsidian.md/lib/scrypt.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.897}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 4.963, "request": {"method": "GET", "url": "app://obsidian.md/lib/turndown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.963}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 4.708, "request": {"method": "GET", "url": "app://obsidian.md/enhance.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.708}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 11.921, "request": {"method": "GET", "url": "app://obsidian.md/i18n.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 11.921}}, {"startedDateTime": "2025-08-20T13:27:46.018Z", "time": 65.581, "request": {"method": "GET", "url": "app://obsidian.md/app.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 65.581}}, {"startedDateTime": "2025-08-20T13:27:46.117Z", "time": 18.848, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/535a6cf662596b3bd6a6.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 18.848}}, {"startedDateTime": "2025-08-20T13:27:46.117Z", "time": 20.739, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cb10ffd7684cd9836a05.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 20.739}}, {"startedDateTime": "2025-08-20T13:27:46.117Z", "time": 23.345, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cbe0ae49c52c920fd563.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 23.345}}, {"startedDateTime": "2025-08-20T13:27:46.117Z", "time": 26.011, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/b5f0f109bc88052d4000.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 26.011}}, {"startedDateTime": "2025-08-20T13:27:46.117Z", "time": 28.602, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/c8ba52b05a9ef10f4758.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 28.602}}, {"startedDateTime": "2025-08-20T13:27:46.117Z", "time": 33.792, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/2d5198822ab091ce4305.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 33.792}}, {"startedDateTime": "2025-08-20T13:27:46.117Z", "time": 31.153, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/72505e6a122c6acd5471.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 31.153}}, {"startedDateTime": "2025-08-20T13:27:46.289Z", "time": 162.403, "request": {"method": "GET", "url": "app://obsidian.md/worker.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 162.403}}, {"startedDateTime": "2025-08-20T13:27:46.296Z", "time": 146.776, "request": {"method": "GET", "url": "blob:app://obsidian.md/59a76bd2-ca84-412d-978d-b32b1ba7a3ab", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Content-Length", "value": "4587"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 146.776}}]}}