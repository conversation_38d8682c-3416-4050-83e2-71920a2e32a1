{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.54.2"}, "browser": {"name": "electron", "version": "138.0.7204.235"}, "entries": [{"startedDateTime": "2025-08-20T13:34:00.079Z", "time": 3.405, "request": {"method": "GET", "url": "app://obsidian.md/index.html", "httpVersion": "app", "cookies": [], "headers": [], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Content-Type", "value": "text/html"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/html"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 3.405}}, {"startedDateTime": "2025-08-20T13:34:00.162Z", "time": 5.376, "request": {"method": "GET", "url": "app://obsidian.md/app.css", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/css"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/css"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.376}}, {"startedDateTime": "2025-08-20T13:34:00.162Z", "time": 12.844, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/codemirror.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 12.844}}, {"startedDateTime": "2025-08-20T13:34:00.162Z", "time": 4.914, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/overlay.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.914}}, {"startedDateTime": "2025-08-20T13:34:00.162Z", "time": 8.7, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/markdown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.7}}, {"startedDateTime": "2025-08-20T13:34:00.162Z", "time": 8.065, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/cm-addons.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.065}}, {"startedDateTime": "2025-08-20T13:34:00.162Z", "time": 11.959, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/vim.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 11.959}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 4.955, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/meta.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 4.955}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 15.291, "request": {"method": "GET", "url": "app://obsidian.md/lib/moment.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 15.291}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 24.774, "request": {"method": "GET", "url": "app://obsidian.md/lib/pixi.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 24.774}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 8.061, "request": {"method": "GET", "url": "app://obsidian.md/lib/i18next.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.061}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 7.945, "request": {"method": "GET", "url": "app://obsidian.md/lib/scrypt.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 7.945}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 8.154, "request": {"method": "GET", "url": "app://obsidian.md/lib/turndown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.154}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 8.044, "request": {"method": "GET", "url": "app://obsidian.md/enhance.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.044}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 11.15, "request": {"method": "GET", "url": "app://obsidian.md/i18n.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 11.15}}, {"startedDateTime": "2025-08-20T13:34:00.163Z", "time": 63.501, "request": {"method": "GET", "url": "app://obsidian.md/app.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 63.501}}, {"startedDateTime": "2025-08-20T13:34:00.260Z", "time": 20.067, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/535a6cf662596b3bd6a6.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 20.067}}, {"startedDateTime": "2025-08-20T13:34:00.260Z", "time": 22.21, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cb10ffd7684cd9836a05.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 22.21}}, {"startedDateTime": "2025-08-20T13:34:00.260Z", "time": 24.853, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cbe0ae49c52c920fd563.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 24.853}}, {"startedDateTime": "2025-08-20T13:34:00.260Z", "time": 30.32, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/b5f0f109bc88052d4000.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 30.32}}, {"startedDateTime": "2025-08-20T13:34:00.260Z", "time": 27.524, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/c8ba52b05a9ef10f4758.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 27.524}}, {"startedDateTime": "2025-08-20T13:34:00.260Z", "time": 32.885, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/2d5198822ab091ce4305.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 32.885}}, {"startedDateTime": "2025-08-20T13:34:00.261Z", "time": 35.546, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/72505e6a122c6acd5471.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 35.546}}, {"startedDateTime": "2025-08-20T13:34:00.451Z", "time": 257.724, "request": {"method": "GET", "url": "app://obsidian.md/worker.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 257.724}}, {"startedDateTime": "2025-08-20T13:34:00.454Z", "time": 235.765, "request": {"method": "GET", "url": "blob:app://obsidian.md/33a907fb-e3fe-46a7-9e7e-c468313b471c", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Content-Length", "value": "4587"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 235.765}}]}}