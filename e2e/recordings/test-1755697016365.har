{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.54.2"}, "browser": {"name": "electron", "version": "138.0.7204.235"}, "entries": [{"startedDateTime": "2025-08-20T13:36:57.104Z", "time": 10.733, "request": {"method": "GET", "url": "app://obsidian.md/index.html", "httpVersion": "app", "cookies": [], "headers": [], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Content-Type", "value": "text/html"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/html"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 10.733}}, {"startedDateTime": "2025-08-20T13:36:57.253Z", "time": 11.091, "request": {"method": "GET", "url": "app://obsidian.md/app.css", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/css"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/css"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 11.091}}, {"startedDateTime": "2025-08-20T13:36:57.253Z", "time": 30.521, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/codemirror.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 30.521}}, {"startedDateTime": "2025-08-20T13:36:57.253Z", "time": 8.959, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/overlay.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.959}}, {"startedDateTime": "2025-08-20T13:36:57.253Z", "time": 9.03, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/markdown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 9.03}}, {"startedDateTime": "2025-08-20T13:36:57.254Z", "time": 9.019, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/cm-addons.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 9.019}}, {"startedDateTime": "2025-08-20T13:36:57.254Z", "time": 29.153, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/vim.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 29.153}}, {"startedDateTime": "2025-08-20T13:36:57.254Z", "time": 8.931, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/meta.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.931}}, {"startedDateTime": "2025-08-20T13:36:57.254Z", "time": 34.466, "request": {"method": "GET", "url": "app://obsidian.md/lib/moment.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 34.466}}, {"startedDateTime": "2025-08-20T13:36:57.254Z", "time": 39.212, "request": {"method": "GET", "url": "app://obsidian.md/lib/pixi.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 39.212}}, {"startedDateTime": "2025-08-20T13:36:57.255Z", "time": 8.841, "request": {"method": "GET", "url": "app://obsidian.md/lib/i18next.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.841}}, {"startedDateTime": "2025-08-20T13:36:57.255Z", "time": 8.826, "request": {"method": "GET", "url": "app://obsidian.md/lib/scrypt.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.826}}, {"startedDateTime": "2025-08-20T13:36:57.255Z", "time": 8.855, "request": {"method": "GET", "url": "app://obsidian.md/lib/turndown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.855}}, {"startedDateTime": "2025-08-20T13:36:57.255Z", "time": 8.806, "request": {"method": "GET", "url": "app://obsidian.md/enhance.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.806}}, {"startedDateTime": "2025-08-20T13:36:57.257Z", "time": 28.219, "request": {"method": "GET", "url": "app://obsidian.md/i18n.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 28.219}}, {"startedDateTime": "2025-08-20T13:36:57.257Z", "time": 99.691, "request": {"method": "GET", "url": "app://obsidian.md/app.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 99.691}}, {"startedDateTime": "2025-08-20T13:36:57.409Z", "time": 66.784, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/535a6cf662596b3bd6a6.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 66.784}}, {"startedDateTime": "2025-08-20T13:36:57.410Z", "time": 54.473, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cb10ffd7684cd9836a05.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 54.473}}, {"startedDateTime": "2025-08-20T13:36:57.410Z", "time": 74.218, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cbe0ae49c52c920fd563.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 74.218}}, {"startedDateTime": "2025-08-20T13:36:57.410Z", "time": 60.599, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/b5f0f109bc88052d4000.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 60.599}}, {"startedDateTime": "2025-08-20T13:36:57.410Z", "time": 71.405, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/c8ba52b05a9ef10f4758.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 71.405}}, {"startedDateTime": "2025-08-20T13:36:57.410Z", "time": 68.616, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/2d5198822ab091ce4305.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 68.616}}, {"startedDateTime": "2025-08-20T13:36:57.410Z", "time": 65.968, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/72505e6a122c6acd5471.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 65.968}}, {"startedDateTime": "2025-08-20T13:36:57.563Z", "time": 86.078, "request": {"method": "GET", "url": "app://obsidian.md/worker.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 86.078}}, {"startedDateTime": "2025-08-20T13:36:57.587Z", "time": 57.84, "request": {"method": "GET", "url": "blob:app://obsidian.md/07e650ae-6efb-42bd-814a-43016d7266e5", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Content-Length", "value": "4587"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 57.84}}]}}