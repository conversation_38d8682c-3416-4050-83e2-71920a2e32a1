{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.54.2"}, "browser": {"name": "electron", "version": "138.0.7204.235"}, "entries": [{"startedDateTime": "2025-08-20T13:17:26.494Z", "time": 3.199, "request": {"method": "GET", "url": "app://obsidian.md/index.html", "httpVersion": "app", "cookies": [], "headers": [], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Content-Type", "value": "text/html"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/html"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 3.199}}, {"startedDateTime": "2025-08-20T13:17:26.581Z", "time": 5.76, "request": {"method": "GET", "url": "app://obsidian.md/app.css", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/css"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/css"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.76}}, {"startedDateTime": "2025-08-20T13:17:26.581Z", "time": 12.521, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/codemirror.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 12.521}}, {"startedDateTime": "2025-08-20T13:17:26.581Z", "time": 5.49, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/overlay.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.49}}, {"startedDateTime": "2025-08-20T13:17:26.581Z", "time": 5.496, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/markdown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 5.496}}, {"startedDateTime": "2025-08-20T13:17:26.582Z", "time": 8.619, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/cm-addons.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.619}}, {"startedDateTime": "2025-08-20T13:17:26.582Z", "time": 11.305, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/vim.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 11.305}}, {"startedDateTime": "2025-08-20T13:17:26.582Z", "time": 8.492, "request": {"method": "GET", "url": "app://obsidian.md/lib/codemirror/meta.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.492}}, {"startedDateTime": "2025-08-20T13:17:26.582Z", "time": 15.036, "request": {"method": "GET", "url": "app://obsidian.md/lib/moment.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 15.036}}, {"startedDateTime": "2025-08-20T13:17:26.583Z", "time": 34.165, "request": {"method": "GET", "url": "app://obsidian.md/lib/pixi.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 34.165}}, {"startedDateTime": "2025-08-20T13:17:26.583Z", "time": 8.489, "request": {"method": "GET", "url": "app://obsidian.md/lib/i18next.min.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.489}}, {"startedDateTime": "2025-08-20T13:17:26.583Z", "time": 8.557, "request": {"method": "GET", "url": "app://obsidian.md/lib/scrypt.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.557}}, {"startedDateTime": "2025-08-20T13:17:26.583Z", "time": 8.616, "request": {"method": "GET", "url": "app://obsidian.md/lib/turndown.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.616}}, {"startedDateTime": "2025-08-20T13:17:26.583Z", "time": 8.525, "request": {"method": "GET", "url": "app://obsidian.md/enhance.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 8.525}}, {"startedDateTime": "2025-08-20T13:17:26.583Z", "time": 11.645, "request": {"method": "GET", "url": "app://obsidian.md/i18n.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 11.645}}, {"startedDateTime": "2025-08-20T13:17:26.583Z", "time": 65.027, "request": {"method": "GET", "url": "app://obsidian.md/app.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 65.027}}, {"startedDateTime": "2025-08-20T13:17:26.679Z", "time": 28.823, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/535a6cf662596b3bd6a6.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 28.823}}, {"startedDateTime": "2025-08-20T13:17:26.679Z", "time": 23.125, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cb10ffd7684cd9836a05.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 23.125}}, {"startedDateTime": "2025-08-20T13:17:26.679Z", "time": 18.234, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/cbe0ae49c52c920fd563.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 18.234}}, {"startedDateTime": "2025-08-20T13:17:26.679Z", "time": 28.539, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/b5f0f109bc88052d4000.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 28.539}}, {"startedDateTime": "2025-08-20T13:17:26.679Z", "time": 31.001, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/c8ba52b05a9ef10f4758.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 31.001}}, {"startedDateTime": "2025-08-20T13:17:26.679Z", "time": 33.43, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/2d5198822ab091ce4305.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 33.43}}, {"startedDateTime": "2025-08-20T13:17:26.679Z", "time": 35.979, "request": {"method": "GET", "url": "app://obsidian.md/public/fonts/72505e6a122c6acd5471.woff2", "httpVersion": "app", "cookies": [], "headers": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.235 Electron/37.3.1 Safari/537.36"}, {"name": "Origin", "value": "app://obsidian.md"}, {"name": "<PERSON><PERSON><PERSON>", "value": ""}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "application/octet-stream"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "application/octet-stream"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 35.979}}, {"startedDateTime": "2025-08-20T13:17:26.853Z", "time": 162.566, "request": {"method": "GET", "url": "app://obsidian.md/worker.js", "httpVersion": "app", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "app", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "X-Frame-Options", "value": "DENY"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 162.566}}, {"startedDateTime": "2025-08-20T13:17:26.855Z", "time": 136.726, "request": {"method": "GET", "url": "blob:app://obsidian.md/d5a00de9-65df-49c0-bcaa-8e0a69a2ede0", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Accept", "value": "*/*"}], "queryString": [], "headersSize": -1, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "blob", "cookies": [], "headers": [{"name": "Content-Type", "value": "text/javascript"}, {"name": "Content-Length", "value": "4587"}], "content": {"size": -1, "mimeType": "text/javascript"}, "headersSize": -1, "bodySize": -1, "redirectURL": ""}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": 136.726}}]}}